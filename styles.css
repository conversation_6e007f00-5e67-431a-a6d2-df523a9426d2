/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Arial", sans-serif;
    line-height: 1.6;
    color: #2e373f;
    background: linear-gradient(
        135deg,
        #ffffff 0%,
        #fafafa 25%,
        #f8f8f8 50%,
        #fafafa 75%,
        #ffffff 100%
    );
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
header {
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.98) 0%,
        rgba(250, 250, 250, 0.95) 100%
    );
    backdrop-filter: blur(15px);
    color: #2e373f;
    padding: 1rem 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 25px rgba(46, 55, 63, 0.08);
    border-bottom: 1px solid rgba(173, 217, 41, 0.1);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 2rem;
    font-weight: bold;
    text-shadow: 0 0 20px rgba(173, 217, 41, 0.3);
}

.logo .crowd {
    color: #2e373f;
}

.logo .snap {
    color: #add929;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-links a {
    color: #2e373f;
    text-decoration: none;
    transition: all 0.3s;
    position: relative;
}

.nav-links a:hover {
    color: #add929;
    text-shadow: 0 0 10px rgba(173, 217, 41, 0.5);
}

.nav-links a::after {
    content: "";
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(45deg, #add929, #2e373f);
    transition: width 0.3s;
}

.nav-links a:hover::after {
    width: 100%;
}

.mobile-menu {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.mobile-menu span {
    width: 25px;
    height: 3px;
    background: linear-gradient(45deg, #add929, #2e373f);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: #ffffff;
    height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding-top: 80px;
}

.hero::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(
            circle at 20% 30%,
            rgba(173, 217, 41, 0.1) 0%,
            transparent 50%
        ),
        radial-gradient(
            circle at 80% 70%,
            rgba(173, 217, 41, 0.05) 0%,
            transparent 50%
        ),
        radial-gradient(
            circle at 50% 50%,
            rgba(255, 255, 255, 0.02) 0%,
            transparent 50%
        );
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    gap: 3rem;
    z-index: 2;
}

.hero-text h1 {
    font-size: 4rem;
    margin-bottom: 1rem;
    margin-top: 2rem;
    background: linear-gradient(45deg, #2e373f, #add929, #2e373f);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: fadeInUp 1s ease-out;
    line-height: 1.2;
}

.hero-text .subtitle {
    font-size: 1.5rem;
    color: #add929;
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease-out 0.3s both;
}

.hero-benefits {
    margin: 2rem 0;
}

.benefit-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    color: #2e373f;
}

.checkmark {
    color: #add929;
    font-weight: bold;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.hero-dashboard {
    position: relative;
    padding: 2rem;
}

.dashboard-mockup {
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(240, 245, 255, 0.9) 100%
    );
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: 0 20px 60px rgba(173, 217, 41, 0.2);
    border: 1px solid rgba(173, 217, 41, 0.2);
    position: relative;
    overflow: hidden;
}

.dashboard-header {
    margin-bottom: 1.5rem;
}

.dashboard-nav {
    display: flex;
    gap: 2rem;
}

.nav-item {
    color: #666;
    font-size: 0.9rem;
    cursor: pointer;
}

.nav-item.active {
    color: #2e373f;
    font-weight: bold;
    border-bottom: 2px solid #add929;
    padding-bottom: 0.5rem;
}

.dashboard-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 1rem;
    height: 300px;
}

.dashboard-card {
    background: white;
    border-radius: 15px;
    padding: 1rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.chart-card {
    grid-row: span 2;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-placeholder {
    width: 100%;
    height: 150px;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 10px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-line {
    width: 80%;
    height: 2px;
    background: linear-gradient(90deg, #add929, #2196f3);
    position: relative;
}

.chart-dots {
    position: absolute;
    display: flex;
    gap: 1rem;
    top: 50%;
    transform: translateY(-50%);
}

.dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #add929;
}

.event-card {
    color: #2e373f;
}

.event-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.event-header h3 {
    font-size: 1rem;
    margin: 0;
}

.register-btn {
    background: #add929;
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
}

.event-details {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.event-image {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #add929, #2196f3);
    border-radius: 8px;
}

.event-info p {
    margin: 0.2rem 0;
    font-size: 0.8rem;
    color: #666;
}

.assistant-card {
    background: linear-gradient(135deg, #2e373f, #1a1a1a);
    color: white;
}

.assistant-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-weight: bold;
    font-size: 0.9rem;
}

.assistant-message p {
    font-size: 0.8rem;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.assistant-btn {
    background: #add929;
    color: #2e373f;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    cursor: pointer;
}

.floating-notification {
    position: absolute;
    bottom: -10px;
    right: 20px;
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #2e373f;
    border: 1px solid rgba(173, 217, 41, 0.3);
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.cta-button {
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: bold;
    text-decoration: none;
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
}

.cta-primary {
    background: linear-gradient(45deg, #add929, #2e373f);
    color: white;
    box-shadow: 0 10px 30px rgba(173, 217, 41, 0.3);
}

.cta-secondary {
    background: transparent;
    color: #add929;
    border: 2px solid #add929;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(173, 217, 41, 0.4);
}

/* About Section */
.about {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f8f8 0%, #ffffff 100%);
    color: #2e373f;
    position: relative;
}

.about::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(
            circle at 30% 20%,
            rgba(173, 217, 41, 0.05) 0%,
            transparent 50%
        ),
        radial-gradient(
            circle at 70% 80%,
            rgba(173, 217, 41, 0.03) 0%,
            transparent 50%
        );
}

.section-title {
    text-align: center;
    font-size: 3rem;
    margin-bottom: 2rem;
    background: linear-gradient(45deg, #2e373f, #add929);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.about-content {
    text-align: center;
    font-size: 1.3rem;
    line-height: 1.8;
    max-width: 800px;
    margin: 0 auto 3rem;
    color: rgba(46, 55, 63, 0.9);
}

/* Features Section */
.features {
    padding: 80px 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
    position: relative;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(250, 250, 250, 0.9) 100%
    );
    backdrop-filter: blur(15px);
    padding: 2.5rem;
    border-radius: 20px;
    text-align: left;
    box-shadow: 0 10px 40px rgba(46, 55, 63, 0.08);
    border: 1px solid rgba(173, 217, 41, 0.15);
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(45deg, #add929, transparent);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(173, 217, 41, 0.2);
    border-color: rgba(173, 217, 41, 0.4);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #add929;
}

.feature-card p {
    color: rgba(46, 55, 63, 0.8);
    line-height: 1.6;
}

/* Benefits Section */
.benefits {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f8f8 0%, #ffffff 100%);
    color: #2e373f;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.benefit {
    text-align: center;
    padding: 2rem 1rem;
    background: linear-gradient(
        135deg,
        rgba(173, 217, 41, 0.1) 0%,
        transparent 100%
    );
    border-radius: 15px;
    transition: all 0.3s;
}

.benefit:hover {
    transform: translateY(-5px);
    background: linear-gradient(
        135deg,
        rgba(173, 217, 41, 0.2) 0%,
        transparent 100%
    );
}

.benefit-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
}

.benefit h3 {
    color: #add929;
    margin-bottom: 0.5rem;
}

/* CTA Section */
.final-cta {
    padding: 80px 0;
    background: linear-gradient(
        135deg,
        #ffffff 0%,
        #f8f8f8 50%,
        #ffffff 100%
    );
    text-align: center;
    color: #2e373f;
    position: relative;
}

.final-cta::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
        circle at 50% 50%,
        rgba(173, 217, 41, 0.1) 0%,
        transparent 70%
    );
}

.final-cta h2 {
    font-size: 3rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #2e373f, #add929, #2e373f);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.final-cta p {
    font-size: 1.3rem;
    margin-bottom: 3rem;
    color: rgba(46, 55, 63, 0.9);
}

/* Footer */
footer {
    background: #f8f8f8;
    color: rgba(46, 55, 63, 0.7);
    text-align: center;
    padding: 2rem 0;
    border-top: 1px solid rgba(173, 217, 41, 0.2);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%,
    100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Exhibition Gallery */
.exhibition-showcase {
    background: linear-gradient(
        135deg,
        rgba(173, 217, 41, 0.1) 0%,
        rgba(46, 55, 63, 0.8) 100%
    ) !important;
}

.exhibition-gallery {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 1.5rem;
    border-radius: 15px;
    overflow: hidden;
}

.exhibition-gallery img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 10px;
    transition: all 0.3s ease;
    filter: brightness(0.8);
}

.exhibition-gallery img:hover {
    transform: scale(1.05);
    filter: brightness(1);
    box-shadow: 0 5px 20px rgba(173, 217, 41, 0.3);
}

/* Solution Section */
.solution {
    padding: 80px 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
    color: #2e373f;
}

.solution-subtitle {
    text-align: center;
    font-size: 1.3rem;
    color: #add929;
    margin-bottom: 3rem;
}

.solution-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.solution-item {
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(250, 250, 250, 0.9) 100%
    );
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid rgba(173, 217, 41, 0.15);
    transition: all 0.3s ease;
}

.solution-item:hover {
    transform: translateY(-5px);
    border-color: rgba(173, 217, 41, 0.4);
    box-shadow: 0 10px 30px rgba(173, 217, 41, 0.2);
}

.solution-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.solution-item h3 {
    color: #add929;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.solution-item p {
    color: rgba(46, 55, 63, 0.8);
    line-height: 1.6;
}

/* Platform Highlights */
.platform-highlights {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f8f8 0%, #ffffff 100%);
    color: #2e373f;
}

.highlights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.highlight-card {
    background: linear-gradient(
        135deg,
        rgba(173, 217, 41, 0.1) 0%,
        rgba(46, 55, 63, 0.3) 100%
    );
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    border: 1px solid rgba(173, 217, 41, 0.2);
    transition: all 0.3s ease;
}

.highlight-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(173, 217, 41, 0.3);
}

.highlight-image {
    margin-bottom: 1.5rem;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(
        135deg,
        rgba(173, 217, 41, 0.2) 0%,
        transparent 100%
    );
    border-radius: 15px;
}

.highlight-image div {
    font-size: 4rem;
    opacity: 0.8;
}

.highlight-card h3 {
    color: #add929;
    margin-bottom: 1rem;
}

/* How It Works */
.how-it-works {
    padding: 80px 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
    color: #2e373f;
}

.steps-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 3rem;
    flex-wrap: wrap;
    gap: 2rem;
}

.step {
    text-align: center;
    flex: 1;
    min-width: 200px;
    position: relative;
}

.step-number {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #add929, #2e373f);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto 1rem;
    box-shadow: 0 5px 20px rgba(173, 217, 41, 0.3);
}

.step h3 {
    color: #add929;
    margin-bottom: 0.5rem;
}

.step:not(:last-child)::after {
    content: "→";
    position: absolute;
    right: -1rem;
    top: 30px;
    color: rgba(173, 217, 41, 0.5);
    font-size: 2rem;
}

/* CrowdSnap in Action */
.crowdsnap-action {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f8f8 0%, #ffffff 100%);
    color: #2e373f;
}

.action-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.action-image {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.action-image:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(173, 217, 41, 0.2);
}

.action-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.action-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(
        transparent,
        rgba(232, 245, 232, 0.95)
    );
    padding: 2rem;
    color: #2e373f;
}

.action-overlay h3 {
    color: #add929;
    margin-bottom: 0.5rem;
}

/* Enhanced Benefits */
.benefit p {
    color: rgba(46, 55, 63, 0.7);
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

/* Particle background */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: rgba(173, 217, 41, 0.3);
    border-radius: 50%;
    animation: particle-float 6s infinite ease-in-out;
}

@keyframes particle-float {
    0%,
    100% {
        transform: translateY(0px) translateX(0px);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
}

.creative-gallery {
    position: relative;
    padding: 2rem;
    background: linear-gradient(135deg, rgba(173, 217, 41, 0.05) 0%, rgba(46, 55, 63, 0.02) 100%);
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.gallery-main {
    position: relative;
    margin-bottom: 1.5rem;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 50px rgba(173, 217, 41, 0.2);
}

.main-image {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.gallery-main:hover .main-image {
    transform: scale(1.05);
}

.gallery-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(46, 55, 63, 0.9));
    color: white;
    padding: 2rem;
}

.gallery-overlay h3 {
    color: #add929;
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.gallery-item {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(173, 217, 41, 0.2);
}

.gallery-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
    filter: brightness(1.1);
}

.item-label {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(173, 217, 41, 0.9));
    color: white;
    padding: 0.8rem;
    font-size: 0.9rem;
    font-weight: bold;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .mobile-menu {
        display: flex;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }

    .hero-text .subtitle {
        font-size: 1.2rem;
    }

    .hero-image::before {
        font-size: 8rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .solution-grid {
        grid-template-columns: 1fr;
    }

    .highlights-grid {
        grid-template-columns: 1fr;
    }

    .steps-container {
        flex-direction: column;
    }

    .step:not(:last-child)::after {
        content: "↓";
        right: auto;
        top: auto;
        bottom: -1rem;
    }

    .action-gallery {
        grid-template-columns: 1fr;
    }
}
